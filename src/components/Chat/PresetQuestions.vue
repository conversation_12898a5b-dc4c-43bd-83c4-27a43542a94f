<template>
  <div class="preset-questions-container">
    <div ref="scrollRef" class="preset-questions-scroll">
      <div
        v-for="(question, index) in questions"
        :key="index"
        class="preset-question-item"
        :class="{ clicked: clickedIndex === index }"
        @click="handleQuestionClick(question, $event)"
        @touchend="handleTouchEnd"
      >
        {{ question }}
      </div>
    </div>
    <!-- 滑动提示指示器 -->
    <div v-if="showIndicator && showScrollIndicator" class="scroll-indicator">
      <div class="scroll-dots">
        <div v-for="i in 3" :key="i" class="dot"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';

// Props 定义
interface IProps {
  questions: string[]; // 问题列表
  showIndicator?: boolean; // 是否显示滑动指示器
  maxItemWidth?: string; // 单个问题项的最大宽度
}

const props = withDefaults(defineProps<IProps>(), {
  showIndicator: true,
  maxItemWidth: '400px',
});

// Emits 定义
const emit = defineEmits<{
  (e: 'question-click', question: string): void;
}>();

// 响应式数据
const scrollRef = ref<HTMLElement | null>(null);
const showScrollIndicator = ref(true);
const clickedIndex = ref<number | null>(null);

// 处理问题点击
const handleQuestionClick = (question: string, event?: Event) => {
  console.log('🎯 [PresetQuestions] 点击预设问题:', question);

  // 获取点击的问题索引，用于显示点击反馈
  const questionIndex = props.questions.findIndex((q) => q === question);
  if (questionIndex !== -1) {
    clickedIndex.value = questionIndex;
    // 300ms后清除点击状态
    setTimeout(() => {
      clickedIndex.value = null;
    }, 300);
  }

  // 在触摸设备上，确保点击后移除焦点，避免hover状态持续
  if (event && event.target instanceof HTMLElement) {
    event.target.blur();
    // 强制移除任何可能的hover状态
    setTimeout(() => {
      if (event.target instanceof HTMLElement) {
        event.target.blur();
      }
    }, 100);
  }

  emit('question-click', question);
};

// 处理触摸结束事件，确保移动端状态正确恢复
const handleTouchEnd = (event: TouchEvent) => {
  if (event.target instanceof HTMLElement) {
    // 移除焦点，防止hover状态持续
    event.target.blur();
    // 延迟一点时间再次确保状态清除
    setTimeout(() => {
      if (event.target instanceof HTMLElement) {
        event.target.blur();
      }
    }, 50);
  }
};

// 监听预设问题变化
watch(
  () => props.questions,
  (newQuestions, oldQuestions) => {
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    if (isIOS) {
      console.log('🎯 [PresetQuestions] iOS设备 - 预设问题变化:', {
        oldCount: oldQuestions?.length || 0,
        newCount: newQuestions.length,
        oldQuestions: oldQuestions || [],
        newQuestions,
        timestamp: new Date().toISOString(),
      });
    }
  },
  { deep: true },
);

// 组件挂载后设置滑动监听
onMounted(() => {
  // iOS设备调试信息
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
  const isIOSSafari = isIOS && /Safari/.test(navigator.userAgent) && !/CriOS|FxiOS/.test(navigator.userAgent);

  console.log('🎯 [PresetQuestions] 组件挂载:', {
    isIOS,
    isIOSSafari,
    questionsCount: props.questions.length,
    questions: props.questions,
    scrollRefExists: !!scrollRef.value,
    showIndicator: props.showIndicator,
  });

  if (scrollRef.value && props.showIndicator) {
    scrollRef.value.addEventListener(
      'scroll',
      () => {
        // 用户开始滑动后隐藏指示器
        showScrollIndicator.value = false;
      },
      { once: true },
    );
  }
});
</script>

<style lang="scss" scoped>
@import '@/styles/util.scss';
@import '@/styles/variable.scss';

.preset-questions-container {
  width: 100%;
  padding: var(--spacing-lg) var(--spacing-lg) 0px var(--spacing-lg);
  background: transparent;
  position: relative;

  // iOS Safari 兼容性修复
  @supports (-webkit-touch-callout: none) {
    // 确保在iOS上正确显示
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: transform;
    // 修复iOS Safari中可能的显示问题
    z-index: 10;
  }

  .preset-questions-scroll {
    display: flex;
    gap: var(--spacing-md);
    overflow-x: auto;
    padding: var(--spacing-xs) 0 var(--spacing-sm) 0;
    scrollbar-width: none;
    -ms-overflow-style: none;

    // iOS Safari 滚动优化
    @supports (-webkit-touch-callout: none) {
      -webkit-overflow-scrolling: touch;
      // 确保在iOS上能正确滚动
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
    }

    &::-webkit-scrollbar {
      display: none;
    }

    .preset-question-item {
      flex-shrink: 0;
      padding: var(--spacing-md) var(--spacing-xl);
      background: var(--bg-glass);
      border: 1px solid var(--border-light);
      border-radius: var(--border-radius-full);
      color: var(--text-primary);
      font-size: var(--font-size-2xl);
      font-weight: 500;
      line-height: 1.3;
      white-space: nowrap;
      cursor: pointer;
      transition: all 0.3s ease;
      // 毛玻璃效果
      backdrop-filter: blur(10px);
      box-shadow: var(--shadow-medium);
      min-height: 56px;
      display: flex;
      align-items: center;
      justify-content: center;
      max-width: v-bind('props.maxItemWidth');

      // 移动端触摸优化
      -webkit-tap-highlight-color: transparent;
      user-select: none;
      // 防止iOS Safari的触摸高亮
      -webkit-touch-callout: none;
      // 确保触摸后状态能正确恢复
      touch-action: manipulation;

      // iOS Safari 兼容性修复
      @supports (-webkit-touch-callout: none) {
        // 确保在iOS上正确显示和交互
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        // iOS Safari 毛玻璃效果兼容性
        -webkit-backdrop-filter: blur(10px);
        // 确保背景色在iOS上正确显示
        background: var(--bg-glass) !important;
        border-color: var(--border-light) !important;
      }

      // 只在支持hover的设备上启用hover效果（排除触摸设备）
      @media (hover: hover) and (pointer: fine) {
        &:hover {
          background: var(--primary-color-light);
          border-color: var(--primary-color);
          transform: translateY(-2px);
          box-shadow: 0px 6px 16px var(--primary-color-strong);
          color: var(--primary-color);
        }
      }

      // 触摸设备的点击反馈
      &:active {
        transform: translateY(0px) scale(0.96);
        background: var(--primary-color-medium);
        border-color: var(--primary-color);
        box-shadow: var(--shadow-medium);
        color: var(--primary-color);
      }

      // 确保触摸结束后状态能正确恢复
      &:focus {
        outline: none;
      }

      // 点击反馈效果
      &.clicked {
        background: var(--primary-color-medium) !important;
        border-color: var(--primary-color) !important;
        color: var(--primary-color) !important;
        transform: scale(0.95) !important;
        box-shadow: 0px 2px 8px var(--primary-color-strong) !important;
        transition: all 0.15s ease !important;
      }

      // 移动端专用：确保点击后状态恢复
      @media (max-width: 768px) {
        &:hover {
          // 在移动端完全禁用hover效果
          background: var(--bg-glass) !important;
          border-color: var(--border-light) !important;
          transform: none !important;
          box-shadow: var(--shadow-medium) !important;
          color: var(--text-primary) !important;
        }

        // 移动端点击反馈更明显
        &.clicked {
          background: var(--primary-color-light) !important;
          border-color: var(--primary-color) !important;
          color: var(--primary-color) !important;
          transform: scale(0.92) !important;
          box-shadow: 0px 4px 12px var(--primary-color-strong) !important;
        }
      }

      // 添加一些视觉层次感
      &:first-child {
        margin-left: var(--spacing-xs);
      }

      &:last-child {
        margin-right: var(--spacing-xs);
      }

      // 响应式字体大小和内边距
      @media (max-width: 375px) {
        font-size: var(--font-size-xl);
        padding: var(--spacing-md) var(--spacing-lg);
        min-height: 52px;
      }

      @media (min-width: 414px) {
        font-size: var(--font-size-2xl);
        padding: var(--spacing-lg) var(--spacing-xl);
        min-height: 60px;
      }
    }
  }

  // 滑动指示器样式 - 更新为赛博朋克风格
  .scroll-indicator {
    position: absolute;
    bottom: var(--spacing-xs);
    right: var(--spacing-lg);
    opacity: 0.6;

    .scroll-dots {
      display: flex;
      gap: var(--spacing-xs);

      .dot {
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background: var(--text-quaternary);
        animation: scrollHint 2s infinite ease-in-out;
        box-shadow: 0 0 4px rgba(0, 255, 255, 0.3); // 轻微发光

        &:nth-child(2) {
          animation-delay: 0.2s;
        }

        &:nth-child(3) {
          animation-delay: 0.4s;
        }
      }
    }
  }
}

// 滑动提示动画 - 增强发光效果
@keyframes scrollHint {
  0%,
  100% {
    opacity: 0.4;
    transform: scale(1);
    box-shadow: 0 0 4px rgba(0, 255, 255, 0.2);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2);
    box-shadow: 0 0 8px rgba(0, 255, 255, 0.5);
  }
}
</style>
