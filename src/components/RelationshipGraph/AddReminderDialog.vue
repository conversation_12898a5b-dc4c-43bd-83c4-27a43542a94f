<template>
  <div v-if="show" class="dialog-overlay">
    <div class="dialog-container edit-dialog">
      <div class="dialog-header">
        <div class="dialog-title">{{ isEditMode ? '编辑事项' : '添加事项' }}</div>

        <div class="dialog-close" @click="handleClose">
          <img src="@/assets/icon/close.png" alt="关闭" class="close-icon" />
        </div>
      </div>

      <div class="dialog-content edit-content">
        <!-- 文本输入模式 - 只在编辑模式下显示 -->
        <div v-if="isEditMode" class="text-input-section">
          <!-- 提醒文本 -->
          <div class="input-group">
            <label class="input-label">提醒内容</label>
            <input
              v-model="formData.reminder_text_template"
              type="text"
              class="input-field"
              placeholder="请输入提醒内容"
              maxlength="200"
            />
          </div>

          <!-- 基础事件日期 -->
          <div class="input-group">
            <label class="input-label">基础事件日期 *</label>
            <input
              v-model="formData.base_event_date"
              type="datetime-local"
              class="input-field datetime-input"
              placeholder="请输入基础事件日期"
              required
            />
          </div>

          <!-- 下次触发时间 -->
          <div class="input-group">
            <label class="input-label">下次触发时间 *</label>
            <input
              v-model="formData.next_trigger_time"
              type="datetime-local"
              class="input-field datetime-input"
              placeholder="请输入下次触发时间"
              required
            />
          </div>

          <!-- 重复规则 -->
          <div class="input-group">
            <label class="input-label">重复规则</label>
            <select v-model="formData.recurrence_rule" class="input-field">
              <option value="">不重复</option>
              <option value="daily">每天</option>
              <option value="weekly">每周</option>
              <option value="monthly">每月</option>
              <option value="yearly">每年</option>
            </select>
          </div>

          <!-- 提前通知 -->
          <div class="input-group">
            <label class="input-label">提前通知（分钟）</label>
            <input
              v-model.number="advanceMinutes"
              type="number"
              class="input-field"
              placeholder="0"
              min="0"
              max="1440"
            />
          </div>
        </div>

        <!-- 语音输入模式 - 只在添加模式下显示 -->
        <div v-if="!isEditMode" class="voice-input-section">
          <!-- 语音对话区域 -->
          <div class="voice-chat-section">
            <!-- 聊天消息列表 -->
            <div ref="chatMessagesRef" class="chat-messages">
              <div v-for="message in chatMessages" :key="message.key" class="chat-message" :class="message.role">
                <div
                  class="message-content"
                  :class="{ 'loading-content': !message.isFinish && message.role === 'assistant' }"
                >
                  <!-- 显示消息内容 -->
                  <div v-if="message.content || message.isFinish">{{ message.content }}</div>
                  <!-- 显示loading动画 -->
                  <div v-else-if="!message.isFinish && message.role === 'assistant'" class="loading">
                    <div class="dot"></div>
                    <div class="dot"></div>
                    <div class="dot"></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 语音输入区域 -->
            <div class="voice-input-area">
              <!-- 识别文字显示区域 -->
              <div v-if="recognizedText" class="recognized-text">
                {{ recognizedText }}
              </div>

              <!-- 默认状态：左侧语音按钮 + 中间输入框 + 右侧发送按钮 -->
              <div v-if="!isRecording" class="voice-input-default">
                <button class="voice-mic-btn" @click="startVoiceRecording">
                  <div class="voice-button-bg"></div>
                  <img src="@/assets/icon/mic.png" alt="语音" class="voice-mic-icon" />
                </button>

                <div class="text-input-container">
                  <input
                    v-model="textInput"
                    type="text"
                    placeholder="聊天来添加提醒事项吧~"
                    class="text-input"
                    @keydown.enter="handleTextSend"
                  />
                </div>

                <button
                  class="send-btn"
                  :class="{
                    'not-input': !textInput.trim() || chatStore.answerStatus === AnswerStatusEnum.LOADING,
                  }"
                  @click="handleTextSend"
                >
                  <i class="iconfont icon-mobile-send" class-prefix="icon"></i>
                </button>
              </div>

              <!-- 录音状态：左侧麦克风 + 语音条 -->
              <div v-else class="voice-input-recording">
                <div class="recording-mic-container">
                  <button class="recording-mic-btn" @click="startVoiceRecording">
                    <div class="voice-button-bg recording"></div>
                    <img src="@/assets/icon/mic.png" alt="录音中" class="recording-mic-icon" />
                  </button>
                </div>
                <div class="voice-wave">
                  <div v-for="index in 50" :key="index" class="wave-line"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="isEditMode" class="dialog-footer">
        <button class="cancel-btn" :disabled="saving" @click="handleClose">取消</button>
        <button class="confirm-btn" :disabled="saving || !isFormValid" @click="handleSubmit">
          <span v-if="saving">保存中...</span>
          <span v-else>{{ isEditMode ? '确认修改' : '确认添加' }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
import {
  addReminder,
  updateReminder,
  type IAddReminderRequest,
  type IUpdateReminderRequest,
  type IReminder,
} from '@/apis/memory';
import { showFailToast, showSuccessToast, showToast } from 'vant';
import { streamChat, createConversation, type IToolCall } from '@/apis/chat';
import { Typewriter } from '@/utils/typeWriter';
import { useChatStore } from '@/stores/chat';
import { AnswerStatusEnum } from '@/constants/chat';
import { getStreamAsr } from '@/apis/chat';
import { generateRandomString } from '@/utils';
import { debounce } from 'lodash-es';
import Recorder from 'recorder-realtime';

// 聊天消息类型定义
interface IChatStreamContent {
  role: 'user' | 'assistant';
  content: string;
  key: number | string;
  isFinish: boolean;
}

// Props定义
interface IProps {
  show: boolean;
  userId: string;
  personId?: string;
  editReminder?: IReminder | null; // 编辑的提醒数据
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  close: [];
  success: [];
}>();

// 响应式数据
const saving = ref(false);
const advanceMinutes = ref(0);
const inputMode = ref<'text' | 'voice'>('text'); // 输入模式

// 表单数据
const formData = ref({
  reminder_text_template: '',
  base_event_date: '',
  next_trigger_time: '',
  recurrence_rule: '',
});

// 语音聊天相关状态
const chatStore = useChatStore();
const chatMessages = ref<IChatStreamContent[]>([]);
const chatMessagesRef = ref<HTMLElement>();
const conversationId = ref('');
const streamController = ref<AbortController | null>(null);

// 打字机相关状态
const typewriter = new Typewriter(async (str: string) => {
  if (str && chatMessages.value.length > 0) {
    const lastMessage = chatMessages.value[chatMessages.value.length - 1];
    if (lastMessage.role === 'assistant') {
      lastMessage.content = str;
      await nextTick(() => {
        scrollChatToBottom();
      });
    }
  }
});
const isTypewriterStarted = ref(false);

// 语音录音相关状态
const isRecording = ref(false);
const recognizedText = ref('');
const micPermission = ref(false);
const sessionId = ref('');
const audioBufferIndex = ref(0);
const lastBuffer = ref<ArrayBuffer | null>(null);

// 文字输入相关状态
const textInput = ref('');
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
let timerId: ReturnType<typeof setTimeout> | null = null;
let mediaStream: MediaStream | null = null;

// 计算属性：是否为编辑模式
const isEditMode = computed(() => {
  return !!props.editReminder;
});

// 计算属性：表单验证
const isFormValid = computed(() => {
  return formData.value.base_event_date && formData.value.next_trigger_time;
});

// 切换输入模式
const toggleInputMode = () => {
  inputMode.value = inputMode.value === 'text' ? 'voice' : 'text';

  if (inputMode.value === 'voice') {
    // 切换到语音模式时，初始化聊天会话
    void initChatConversation();
  } else {
    // 切换到文本模式时，清理语音相关状态
    resetChatState();
  }
};

// 初始化聊天会话
const initChatConversation = async () => {
  // 确保聊天状态是干净的
  resetChatState();

  if (!conversationId.value) {
    try {
      const response = await createConversation({
        user_id: props.userId,
      });
      if (response && response.conversation_id) {
        conversationId.value = response.conversation_id;
        console.log('✅ [AddReminderDialog] 聊天会话初始化成功:', conversationId.value);
      }
    } catch (error) {
      console.error('❌ [AddReminderDialog] 初始化聊天会话失败:', error);
    }
  }
};

// 重置聊天状态
const resetChatState = () => {
  if (streamController.value) {
    streamController.value.abort();
    streamController.value = null;
  }

  chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
  isTypewriterStarted.value = false;
  typewriter.done();
  chatMessages.value = [];
};

// 滚动聊天到底部
const scrollChatToBottom = () => {
  if (chatMessagesRef.value) {
    chatMessagesRef.value.scrollTop = chatMessagesRef.value.scrollHeight;
  }
};

// 释放麦克风资源
const releaseMicrophoneResources = () => {
  if (mediaStream) {
    mediaStream.getTracks().forEach((track) => track.stop());
    mediaStream = null;
  }
};

// 设置麦克风权限
const setMicPermission = async () => {
  try {
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    if (!recorder) {
      initRecorder();
    }
  } catch (error) {
    micPermission.value = false;
    showToast('请授权麦克风权限~');
  }
};

// 初始化录音机
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    showToast('录音失败，浏览器不支持录音功能');
    return;
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  recorder.onstart = () => {};

  recorder.onstreamerror = () => {
    showToast('录音失败');
    cancelRecording();
  };

  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      const streamData = await getStreamAsr({
        sessionId: sessionId.value,
        format: 'pcm',
        sampleRate: 16000,
        index: audioBufferIndex.value,
        data: data.buffer,
      });
      if (streamData.data.text) {
        recognizedText.value = streamData.data.full_text;
        await autoSendTimeout();
      }
    }
  };
};

// 两秒不说话自动发送
const autoSendTimeout = debounce(async () => {
  await stopVoiceRecording();
}, 2000);

// 取消录音
const cancelRecording = () => {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }
  releaseMicrophoneResources();
  recognizedText.value = '';
};

// 开始语音录音
const startVoiceRecording = async () => {
  if (isRecording.value) {
    // 如果正在录音，取消录音
    cancelRecording();
    return;
  }

  // 如果没有麦克风权限，先请求权限
  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    recognizedText.value = '';
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopVoiceRecording();
    }, 1000 * 60);
  }
};

// 停止语音录音
const stopVoiceRecording = async () => {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }

  releaseMicrophoneResources();

  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });

  if (recognizedText.value) {
    // 直接发送语音识别的文字
    await handleVoiceSend(recognizedText.value);
  } else {
    showToast('录音解析为空，请重新录制~');
  }
};

// 处理文字发送
const handleTextSend = async () => {
  // 如果输入框为空或正在加载中，直接返回
  if (!textInput.value.trim() || chatStore.answerStatus === AnswerStatusEnum.LOADING) return;

  const content = textInput.value.trim();
  textInput.value = ''; // 清空输入框

  await handleSendMessage(content);
};

// 处理语音发送
const handleVoiceSend = async (content: string) => {
  await handleSendMessage(content);
};

// 通用发送消息方法
const handleSendMessage = async (content: string) => {
  if (!content.trim() || chatStore.answerStatus === AnswerStatusEnum.LOADING) return;

  console.log('🚀 [AddReminderDialog] 开始发送消息:', content);

  // 如果有正在进行的请求，先取消它
  if (streamController.value) {
    console.log('🔄 [AddReminderDialog] 取消正在进行的请求');
    streamController.value.abort();
    streamController.value = null;
  }

  // 清空识别文字
  recognizedText.value = '';

  // 重置状态
  isTypewriterStarted.value = false;
  typewriter.done(); // 确保打字机完全停止

  // 添加用户消息
  chatMessages.value.push({
    role: 'user',
    content,
    key: Date.now(),
    isFinish: true,
  });

  // 添加助手消息占位符
  const assistantMessage: IChatStreamContent = {
    role: 'assistant',
    content: '',
    key: Date.now() + 1,
    isFinish: false,
  };
  chatMessages.value.push(assistantMessage);

  // 滚动到底部
  await nextTick(() => {
    scrollChatToBottom();
  });

  // 设置加载状态
  chatStore.setAnswerStatus(AnswerStatusEnum.LOADING);

  try {
    // 创建新的AbortController
    streamController.value = new AbortController();

    // 构建请求数据 - 专门用于添加提醒的上下文
    const reminderContext = `你是一个智能提醒助手，用户希望通过语音或文字添加提醒事项。请根据用户的输入内容，帮助他们创建合适的提醒。用户可能会说出提醒的内容、时间、重复规则等信息。请分析用户的意图并给出建议。`;
    const finalContent = `${reminderContext}\n\n用户说：${content}`;

    const requestData = {
      content: finalContent,
      conversation_id: conversationId.value,
      user_id: props.userId,
    };

    console.log('📤 [AddReminderDialog] 发送聊天请求:', requestData);

    // 开始流式聊天
    await streamChat(
      requestData,
      {
        onMessage: (messageContent: string) => {
          // 添加数据片段到打字机队列
          typewriter.add(messageContent);

          // 只在第一个数据包到达时启动打字机
          if (!isTypewriterStarted.value) {
            isTypewriterStarted.value = true;
            typewriter.start();
          }
        },
        onPreResponse: (PreResponseContent: string, stage: string) => {
          console.log('🔍 [AddReminderDialog] 收到预响应内容:', PreResponseContent, stage);
        },
        onToolCall: (toolCall: IToolCall) => {
          console.log('🔧 [AddReminderDialog] 工具调用:', toolCall);
        },
        onRecommendations: (recommendations: string[]) => {
          console.log('💡 [AddReminderDialog] 收到推荐问题:', recommendations);

          // 收到推荐问题时，标记消息完成
          assistantMessage.isFinish = true;

          // 结束打字机，立即显示剩余内容
          typewriter.done();

          // 重置状态
          chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);

          // 收到推荐问题表示整个对话真正结束，现在可以安全清空controller
          streamController.value = null;
          console.log('🏁 [AddReminderDialog] 对话完全结束，清空streamController');
        },
        onEnd: () => {
          console.log('✅ [AddReminderDialog] 收到end信号');
        },
        onError: (error: Error) => {
          console.error('❌ [AddReminderDialog] 流式聊天错误:', error);
          assistantMessage.content = '抱歉，发生了错误，请稍后重试。';
          assistantMessage.isFinish = true;
          chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
        },
      },
      streamController.value.signal,
    );
  } catch (error) {
    console.error('❌ [AddReminderDialog] 发送消息失败:', error);
    assistantMessage.content = '抱歉，发生了错误，请稍后重试。';
    assistantMessage.isFinish = true;
    chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
  }

  // 滚动到底部
  await nextTick(() => {
    scrollChatToBottom();
  });
};

// 监听show变化，重置或填充表单
watch(
  () => props.show,
  (newValue) => {
    if (newValue) {
      if (isEditMode.value && props.editReminder) {
        fillFormWithEditData();
        // 编辑模式下使用文本输入模式
        inputMode.value = 'text';
        resetChatState();
      } else {
        resetForm();
        // 添加模式下设置为语音输入模式并初始化聊天会话
        inputMode.value = 'voice';
        void initChatConversation();
      }
    }
  },
);

// 监听editReminder变化
watch(
  () => props.editReminder,
  (newValue) => {
    if (props.show && newValue) {
      fillFormWithEditData();
    }
  },
);

// 重置表单
const resetForm = () => {
  formData.value = {
    reminder_text_template: '',
    base_event_date: '',
    next_trigger_time: '',
    recurrence_rule: '',
  };
  advanceMinutes.value = 0;
};

// 填充编辑数据到表单
const fillFormWithEditData = () => {
  if (!props.editReminder) return;

  const reminder = props.editReminder;

  // 格式化日期时间为datetime-local格式
  const formatDateTimeLocal = (dateString: string) => {
    try {
      const date = new Date(dateString);
      // 转换为本地时间的ISO字符串，去掉秒和毫秒部分
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day}T${hours}:${minutes}`;
    } catch (error) {
      console.error('日期格式化失败:', error);
      return '';
    }
  };

  formData.value = {
    reminder_text_template: reminder.reminder_text_template || '',
    base_event_date: formatDateTimeLocal(reminder.base_event_date),
    next_trigger_time: formatDateTimeLocal(reminder.next_trigger_time),
    recurrence_rule: reminder.recurrence_rule || '',
  };

  // 提取提前通知分钟数
  if (reminder.advance_notice_config && typeof reminder.advance_notice_config === 'object') {
    const config = reminder.advance_notice_config as { minutes?: number };
    advanceMinutes.value = config.minutes || 0;
  } else {
    advanceMinutes.value = 0;
  }
};

// 处理关闭
const handleClose = () => {
  if (saving.value) return;

  // 清理语音相关资源
  resetChatState();
  if (isRecording.value) {
    cancelRecording();
  }
  releaseMicrophoneResources();

  emit('close');
};

// 处理提交
const handleSubmit = async () => {
  if (!isFormValid.value || saving.value) return;

  try {
    saving.value = true;

    if (isEditMode.value) {
      await handleUpdateReminder();
    } else {
      await handleAddReminder();
    }
  } catch (error) {
    console.error('❌ [AddReminderDialog.vue] 操作失败:', error);
    showFailToast('网络错误，操作失败');
  } finally {
    saving.value = false;
  }
};

// 处理添加提醒
const handleAddReminder = async () => {
  console.log('🔄 [AddReminderDialog.vue] 开始添加提醒...', formData.value);

  // 构建请求参数
  const requestData: IAddReminderRequest = {
    user_id: props.userId,
    base_event_date: formData.value.base_event_date,
    next_trigger_time: formData.value.next_trigger_time,
    reminder_text_template: formData.value.reminder_text_template || undefined,
    recurrence_rule: formData.value.recurrence_rule || undefined,
    status: 'active',
  };

  // 如果有人员ID，添加到请求中
  if (props.personId) {
    requestData.subject_person_id = props.personId;
  }

  // 如果有提前通知时间，添加到配置中
  if (advanceMinutes.value > 0) {
    requestData.advance_notice_config = {
      minutes: advanceMinutes.value,
    };
  }

  console.log('📤 [AddReminderDialog.vue] 提醒请求参数:', requestData);

  // 调用添加提醒API
  const response = await addReminder(requestData);

  console.log('📡 [AddReminderDialog.vue] 添加提醒响应:', response);

  if (response && response.success) {
    console.log('✅ [AddReminderDialog.vue] 提醒添加成功');
    showSuccessToast('提醒添加成功！');
    emit('success');
  } else {
    console.warn('⚠️ [AddReminderDialog.vue] 添加提醒失败:', response);
    showFailToast(response?.message || '添加提醒失败');
  }
};

// 处理修改提醒
const handleUpdateReminder = async () => {
  if (!props.editReminder) return;

  console.log('🔄 [AddReminderDialog.vue] 开始修改提醒...', formData.value);

  // 构建请求参数
  const requestData: IUpdateReminderRequest = {
    user_id: props.userId,
    reminder_id: props.editReminder.reminder_id,
    base_event_date: formData.value.base_event_date,
    next_trigger_time: formData.value.next_trigger_time,
    reminder_text_template: formData.value.reminder_text_template || undefined,
    recurrence_rule: formData.value.recurrence_rule || undefined,
    status: 'active',
  };

  // 如果有人员ID，添加到请求中
  if (props.personId) {
    requestData.subject_person_id = props.personId;
  }

  // 如果有提前通知时间，添加到配置中
  if (advanceMinutes.value > 0) {
    requestData.advance_notice_config = {
      minutes: advanceMinutes.value,
    };
  }

  console.log('📤 [AddReminderDialog.vue] 修改提醒请求参数:', requestData);

  // 调用修改提醒API
  const response = await updateReminder(requestData);

  console.log('📡 [AddReminderDialog.vue] 修改提醒响应:', response);

  if (response && response.result === 'success') {
    console.log('✅ [AddReminderDialog.vue] 提醒修改成功');
    showSuccessToast('提醒修改成功！');
    emit('success');
  } else {
    console.warn('⚠️ [AddReminderDialog.vue] 修改提醒失败:', response);
    showFailToast(response?.message || '修改提醒失败');
  }
};

// 组件挂载时的初始化
onMounted(() => {
  // 初始化时不需要做任何事情，等待用户切换到语音模式
});

// 组件卸载时清理
onBeforeUnmount(() => {
  // 清理聊天状态
  resetChatState();

  // 清理语音录音资源
  if (isRecording.value) {
    if (recorder) {
      recorder.stop();
    }
    isRecording.value = false;
  }
  releaseMicrophoneResources();
});
</script>

<style lang="scss" scoped>
// 对话框样式
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

.dialog-container {
  background: rgba(1, 28, 32, 0.6);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  transition: all 0.3s ease;
  width: 90%;
  max-width: 500px;
  color: white;
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;

  &.edit-dialog {
    max-width: 600px;
    height: 800px;
    overflow-y: auto;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 13px;
  margin-bottom: 18px;
  padding-bottom: 18px;
  position: relative;

  .dialog-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 32px;
    font-weight: 600;
  }
  // Tab切换容器
  .tab-switch-container {
    display: flex;
    justify-content: center;
    margin-bottom: 0px;

    .tab-switch {
      position: relative;
      display: flex;
      background: rgba(0, 188, 212, 0.1);
      border: 2px solid #00bcd4;
      border-radius: 28px;
      padding: 6px;
      cursor: pointer;
      transition: all 0.3s ease;
      overflow: hidden;
      width: 320px; // 适度增大宽度以适应更大字体
      min-width: 320px;
      height: 56px;

      &:hover {
        background: rgba(0, 188, 212, 0.15);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
      }

      .tab-slider {
        position: absolute;
        top: 6px;
        left: 6px;
        width: calc(50% - 6px);
        height: calc(100% - 12px);
        background: #00bcd4;
        border-radius: 22px;
        transition: transform 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 188, 212, 0.4);

        &.slide-right {
          transform: translateX(100%);
        }
      }

      .tab-option {
        flex: 1;
        padding: 12px 22px;
        text-align: center;
        font-size: 22px;
        font-weight: 500;
        color: #00bcd4;
        transition: color 0.3s ease;
        position: relative;
        z-index: 1;
        display: flex;
        align-items: center;
        justify-content: center;

        &.active {
          color: white;
        }
      }
    }
  }

  .dialog-close {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.1);
    }

    .close-icon {
      width: 24px;
      height: 24px;
      filter: brightness(0) invert(1);
    }
  }
}

.dialog-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 32px;

  &.edit-content {
    max-height: 60vh;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 3px;

      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    }
  }

  // 文本输入区域
  .text-input-section {
    display: flex;
    flex-direction: column;
    gap: 32px;

    .input-group {
      display: flex;
      flex-direction: column;
      gap: 12px;
      border: 2px solid rgba(0, 188, 212, 0.3);
      border-radius: 16px;
      padding: 20px;
      background: rgba(0, 188, 212, 0.05);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      &:hover {
        border-color: rgba(0, 188, 212, 0.5);
        background: rgba(0, 188, 212, 0.08);
      }

      .input-label {
        color: rgba(255, 255, 255, 0.9);
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 8px;
      }

      .input-field {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 12px;
        padding: 14px 16px;
        color: rgba(255, 255, 255, 0.9);
        font-size: 24px;
        box-sizing: border-box;
        backdrop-filter: blur(10px);
        transition: all 0.2s ease;

        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        &:focus {
          outline: none;
          border-color: rgba(255, 255, 255, 0.5);
          background: rgba(255, 255, 255, 0.15);
          box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
        }

        &.datetime-input {
          color-scheme: dark;
        }
      }
    }
  }

  // 语音输入区域
  .voice-input-section {
    flex: 1;
    display: flex;
    flex-direction: column;

    .voice-chat-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      border: none;
      border-radius: 16px;
      padding: 20px;
      background: rgba(0, 188, 212, 0.05);
      backdrop-filter: blur(10px);
      border: 2px solid rgba(0, 188, 212, 0.3);
      overflow: hidden;

      // 聊天消息列表
      .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 16px 0;
        display: flex;
        flex-direction: column;
        gap: 16px;
        min-height: 200px;

        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: rgba(255, 255, 255, 0.3);
          border-radius: 3px;

          &:hover {
            background: rgba(255, 255, 255, 0.5);
          }
        }

        // 聊天消息
        .chat-message {
          display: flex;
          margin-bottom: 16px;

          &.user {
            justify-content: flex-end;

            .message-content {
              background: rgba(0, 188, 212, 0.2);
              border: 1px solid rgba(0, 188, 212, 0.3);
              border-radius: 18px 18px 4px 18px;
              padding: 12px 16px;
              max-width: 70%;
              color: rgba(255, 255, 255, 0.9);
              font-size: 22px;
              line-height: 1.5;
              word-wrap: break-word;
            }
          }

          &.assistant {
            justify-content: flex-start;

            .message-content {
              background: rgba(255, 255, 255, 0.1);
              border: 1px solid rgba(255, 255, 255, 0.2);
              border-radius: 18px 18px 18px 4px;
              padding: 12px 16px;
              max-width: 70%;
              color: rgba(255, 255, 255, 0.8);
              font-size: 18px;
              line-height: 1.5;
              word-wrap: break-word;

              &.loading-content {
                min-height: 40px;
                display: flex;
                align-items: center;
              }
            }
          }
        }
      }

      // 语音输入区域
      .voice-input-area {
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;

        // 识别文字显示区域
        .recognized-text {
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 12px;
          padding: 12px 16px;
          color: rgba(255, 255, 255, 0.9);
          font-size: 16px;
          text-align: center;
          max-width: 80%;
          word-wrap: break-word;
          margin-bottom: 8px;
        }

        // 默认状态：左侧语音按钮 + 中间输入框 + 右侧发送按钮
        .voice-input-default {
          display: flex;
          align-items: center;
          width: 100%;
          gap: 16px;

          .voice-mic-btn {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            border: 2px solid #00bcd4;
            background: rgba(255, 255, 255, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            backdrop-filter: blur(20px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            flex-shrink: 0;

            &:hover {
              background: rgba(0, 188, 212, 0.1);
              border-color: #00bcd4;
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
            }

            .voice-button-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              border-radius: 50%;
              transition: all 0.3s ease;
            }

            .voice-mic-icon {
              width: 24px;
              height: 24px;
              filter: brightness(0) saturate(100%) invert(77%) sepia(93%) saturate(1352%) hue-rotate(169deg)
                brightness(97%) contrast(96%);
              position: relative;
              z-index: 2;
            }

            &:active {
              transform: scale(0.95);
            }
          }

          .text-input-container {
            flex: 1;
            display: flex;
            align-items: center;

            .text-input {
              width: 100%;
              height: 56px;
              background: rgba(255, 255, 255, 0.1);
              border: 2px solid rgba(255, 255, 255, 0.2);
              border-radius: 28px;
              padding: 0 20px;
              color: rgba(255, 255, 255, 0.9);
              font-size: 20px;
              max-height: 165px;
              font-weight: 600;
              outline: none;
              transition: all 0.3s ease;
              backdrop-filter: blur(20px);

              &::placeholder {
                color: rgba(255, 255, 255, 0.5);
              }

              &:focus {
                border-color: #00bcd4;
                background: rgba(255, 255, 255, 0.15);
                box-shadow: 0 0 0 4px rgba(0, 188, 212, 0.1);
              }
            }
          }

          .send-btn {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            border: 2px solid #00bcd4;
            background: rgba(0, 188, 212, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(20px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            flex-shrink: 0;

            &:hover:not(.not-input) {
              background: rgba(0, 188, 212, 0.2);
              border-color: #00bcd4;
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
            }

            &.not-input {
              opacity: 0.5;
              cursor: not-allowed;
              transform: none;
            }

            .iconfont {
              font-size: 40px;
              color: #00bcd4;
            }

            &:active:not(.not-input) {
              transform: scale(0.95);
            }
          }
        }

        // 录音状态：左侧麦克风 + 语音条
        .voice-input-recording {
          display: flex;
          align-items: center;
          width: 100%;
          gap: 20px;

          .recording-mic-container {
            display: flex;
            align-items: center;
            justify-content: center;

            .recording-mic-btn {
              width: 56px;
              height: 56px;
              border-radius: 50%;
              border: 2px solid #00bcd4;
              background: rgba(255, 255, 255, 0.1);
              cursor: pointer;
              transition: all 0.3s ease;
              display: flex;
              align-items: center;
              justify-content: center;
              position: relative;
              backdrop-filter: blur(20px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

              &:hover {
                background: rgba(0, 188, 212, 0.1);
                border-color: #00bcd4;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
              }

              .voice-button-bg {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border-radius: 50%;
                transition: all 0.3s ease;

                &.recording {
                  background: rgba(0, 188, 212, 0.2);
                  border: 2px solid #00bcd4;
                  animation: voiceRecording 2s ease-in-out infinite;
                  box-shadow: 0 0 0 0 rgba(0, 188, 212, 0.3);
                }
              }

              .recording-mic-icon {
                width: 24px;
                height: 24px;
                filter: brightness(0) saturate(100%) invert(77%) sepia(93%) saturate(1352%) hue-rotate(169deg)
                  brightness(97%) contrast(96%);
                position: relative;
                z-index: 2;
              }

              &:active {
                transform: scale(0.95);
              }
            }
          }

          .voice-wave {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            height: 60px;

            .wave-line {
              width: 3px;
              background: linear-gradient(to top, #00bcd4, #00ffff);
              border-radius: 2px;
              animation: waveAnimation 1.5s ease-in-out infinite;

              &:nth-child(odd) {
                animation-delay: 0.1s;
              }

              &:nth-child(even) {
                animation-delay: 0.3s;
              }
            }
          }
        }
      }
    }
  }
}

// Loading动画
.loading {
  display: flex;
  gap: 4px;
  align-items: center;

  .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    animation: loadingDot 1.4s infinite ease-in-out both;

    &:nth-child(1) {
      animation-delay: -0.32s;
    }

    &:nth-child(2) {
      animation-delay: -0.16s;
    }

    &:nth-child(3) {
      animation-delay: 0s;
    }
  }
}

.dialog-footer {
  display: flex;
  gap: 20px;
  margin-top: 40px;
  padding-top: 32px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);

  .cancel-btn,
  .confirm-btn {
    flex: 1;
    padding: 16px 16px;
    border-radius: 20px;
    font-size: 28px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    background: transparent;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .cancel-btn {
    color: rgba(255, 255, 255, 0.8);
    border-color: rgba(255, 255, 255, 0.8);

    &:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
    }
  }

  .confirm-btn {
    color: #00bcd4;
    border-color: #00bcd4;

    &:hover:not(:disabled) {
      background: rgba(0, 188, 212, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
    }
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes loadingDot {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes voiceRecording {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 188, 212, 0.3);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10px rgba(0, 188, 212, 0.2);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 20px rgba(0, 188, 212, 0);
  }
}

@keyframes waveAnimation {
  0%,
  100% {
    height: 10px;
  }
  50% {
    height: 40px;
  }
}
</style>
