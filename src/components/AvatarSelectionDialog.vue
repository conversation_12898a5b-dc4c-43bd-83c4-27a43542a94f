<template>
  <div class="dialog-overlay">
    <div class="dialog-container">
      <div class="dialog-header">
        <div class="dialog-title">选择头像</div>
        <div class="dialog-close" @click="$emit('close')">
          <img src="@/assets/icon/close.png" alt="关闭" class="close-icon" />
        </div>
      </div>
      <div class="dialog-content">
        <!-- 默认头像选择区域 -->
        <div class="avatar-section">
          <div class="section-title">默认头像</div>
          <div class="avatar-grid">
            <div
              v-for="(avatar, index) in defaultAvatars"
              :key="index"
              class="avatar-option"
              @click="selectAvatar(avatar.id)"
            >
              <img :src="avatar.src" :alt="`默认头像${index + 1}`" class="avatar-preview" />
            </div>
          </div>
        </div>

        <!-- 上传头像区域 -->
        <div class="upload-section">
          <div class="section-title">上传自定义头像</div>
          <div class="upload-button" @click="$emit('upload-avatar')">
            <i class="iconfont icon-camera upload-icon"></i>
            <span class="upload-text">点击上传头像</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getDefaultAvatars } from '@/utils/avatarUtils';

// Emits定义
const emit = defineEmits<{
  close: [];
  'select-avatar': [avatarUrl: string];
  'upload-avatar': [];
}>();

// 默认头像列表
const defaultAvatars = getDefaultAvatars();

// 选择头像
const selectAvatar = (avatarId: string) => {
  emit('select-avatar', avatarId);
};
</script>

<style lang="scss" scoped>
// 对话框样式
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

.dialog-container {
  background: rgba(1, 28, 32, 0.6);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  transition: all 0.3s ease;
  width: 90%;
  max-width: 600px;
  color: white;
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 18px;
  position: relative;

  .dialog-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 40px; // 增加8px (原来32px)
    font-weight: 600;
  }

  .dialog-close {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.1);
    }

    .close-icon {
      width: 24px;
      height: 24px;
      filter: brightness(0) invert(1);
    }
  }
}

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.avatar-section,
.upload-section {
  border: 2px solid rgba(0, 188, 212, 0.3);
  border-radius: 16px;
  padding: 24px;
  background: rgba(0, 188, 212, 0.05);
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.1);
}

.section-title {
  color: #00ffff;
  font-size: 32px; // 增加8px (原来24px)
  font-weight: 600;
  margin-bottom: 20px;
}

.avatar-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.avatar-option {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;

  &:hover {
    border-color: #00ffff;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
    transform: scale(1.1);
  }
}

.avatar-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.upload-section {
  text-align: center;
}

.upload-button {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 24px 32px;
  background: rgba(0, 188, 212, 0.1);
  border: 2px solid #00bcd4;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #00bcd4;

  &:hover {
    background: rgba(0, 188, 212, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
  }
}

.upload-icon {
  font-size: 40px; // 增加8px (原来32px)
}

.upload-text {
  font-size: 28px; // 增加8px (原来20px)
  font-weight: 600;
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
